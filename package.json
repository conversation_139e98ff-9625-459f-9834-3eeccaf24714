{"name": "shoe-landing", "version": "0.1.0", "private": true, "scripts": {"postinstall": "prisma generate", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@formkit/auto-animate": "^0.8.2", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.9.0", "@prisma/nextjs-monorepo-workaround-plugin": "^6.9.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "better-auth": "^1.2.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "googleapis": "^148.0.0", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "motion": "^12.12.1", "next": "15.3.3", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "nodemailer": "^7.0.3", "prisma": "^6.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "short-unique-id": "^5.3.2", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.0", "zod": "^3.25.51", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.1.8", "@types/node": "^22.15.29", "@types/nodemailer": "^6.4.17", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "eslint": "^9.28.0", "eslint-config-next": "15.3.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4.1.8", "typescript": "^5"}, "pnpm": {"onlyBuiltDependencies": ["@prisma/client", "@prisma/engines", "@tailwindcss/oxide", "prisma", "sharp", "unrs-resolver"]}}
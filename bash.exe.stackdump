Stack trace:
Frame         Function      Args
0007FFFF8E90  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF7D90) msys-2.0.dll+0x1FE8E
0007FFFF8E90  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9168) msys-2.0.dll+0x67F9
0007FFFF8E90  000210046832 (000210286019, 0007FFFF8D48, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E90  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8E90  000210068E24 (0007FFFF8EA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9170  00021006A225 (0007FFFF8EA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE0A280000 ntdll.dll
7FFE09110000 KERNEL32.DLL
7FFE077E0000 KERNELBASE.dll
7FFE099F0000 USER32.dll
7FFE07660000 win32u.dll
000210040000 msys-2.0.dll
7FFE08EB0000 GDI32.dll
7FFE07520000 gdi32full.dll
7FFE07D50000 msvcp_win.dll
7FFE073D0000 ucrtbase.dll
7FFE08F30000 advapi32.dll
7FFE083B0000 msvcrt.dll
7FFE098B0000 sechost.dll
7FFE08FF0000 RPCRT4.dll
7FFE06A00000 CRYPTBASE.DLL
7FFE07E00000 bcryptPrimitives.dll
7FFE091E0000 IMM32.DLL

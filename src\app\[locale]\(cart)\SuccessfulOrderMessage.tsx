"use client";

import { useTranslations } from "next-intl";
import Text from "@/components/Text";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { CheckCircle } from "lucide-react";
import { getPaymentDetails, PaymentDetail } from "./paymentDetails";
import { paymentMethods } from "./model";

type SuccessfulOrderMessageProps = {
  className?: string;
  orderId: string;
  paymentMethod: keyof typeof paymentMethods;
  onClose: () => void;
};

const SuccessfulOrderMessage = ({
  className,
  orderId,
  paymentMethod,
  onClose,
}: SuccessfulOrderMessageProps) => {
  const t = useTranslations("CheckoutForm");
  const tSuccess = useTranslations("OrderSuccess");

  // Get payment details for the selected payment method
  const paymentDetails = getPaymentDetails(t, paymentMethod);

  // Extract confirmation note and payment deadline note based on payment method
  const getConfirmationNote = (
    method: keyof typeof paymentMethods,
  ): string | null => {
    switch (method) {
      case paymentMethods.BANK_TRANSFER:
        return t("bankTransferDetails.confirmationNote");
      case paymentMethods.MULTICAIXA_EXPRESS:
        // Multicaixa Express doesn't have a confirmation note
        return null;
      case paymentMethods.PAY_IN_STORE:
        return t("payInStoreDetails.confirmationNote");
      default:
        return t("bankTransferDetails.confirmationNote");
    }
  };

  const getPaymentDeadlineNote = (
    method: keyof typeof paymentMethods,
  ): string => {
    switch (method) {
      case paymentMethods.BANK_TRANSFER:
        return t("bankTransferDetails.paymentDeadlineNote");
      case paymentMethods.MULTICAIXA_EXPRESS:
        return t("multicaixaExpressDetails.paymentDeadlineNote");
      case paymentMethods.PAY_IN_STORE:
        return t("payInStoreDetails.paymentDeadlineNote");
      default:
        return t("bankTransferDetails.paymentDeadlineNote");
    }
  };

  const confirmationNoteText = getConfirmationNote(paymentMethod);
  const paymentDeadlineNoteText = getPaymentDeadlineNote(paymentMethod);

  // Find the notes in payment details
  const confirmationNote = confirmationNoteText
    ? paymentDetails.find(
        (detail: PaymentDetail) => detail.label === confirmationNoteText,
      )
    : null;

  const paymentDeadlineNote = paymentDetails.find(
    (detail: PaymentDetail) => detail.label === paymentDeadlineNoteText,
  );

  return (
    <div
      className={cn(
        "flex flex-col items-center gap-6 py-8 text-center",
        className,
      )}
    >
      {/* Success Icon */}
      <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
        <CheckCircle className="h-8 w-8 text-green-600" />
      </div>

      {/* Success Title */}
      <div className="flex flex-col gap-2">
        <Text as="h3" className="text-2xl font-semibold">
          {tSuccess("title")}
        </Text>
        <Text as="p" size="sm" className="text-muted-foreground">
          {tSuccess("subtitle")}{" "}
          <span className="font-semibold">{orderId}</span>
        </Text>
      </div>

      {/* Payment Instructions */}
      <div className="bg-muted/50 w-full rounded-lg p-6">
        <div className="flex flex-col gap-4 text-left">
          {confirmationNote && (
            <div className="flex flex-col gap-2">
              <Text size="sm" className="font-semibold">
                {tSuccess("confirmationTitle")}
              </Text>
              <Text size="sm" className="text-muted-foreground">
                {confirmationNote.label}
              </Text>
            </div>
          )}

          {paymentDeadlineNote && (
            <div className="flex flex-col gap-2">
              <Text size="sm" className="font-semibold">
                {tSuccess("paymentDeadlineTitle")}
              </Text>
              <Text size="sm" className="text-muted-foreground">
                {paymentDeadlineNote.label}
              </Text>
            </div>
          )}
        </div>
      </div>

      {/* Close Button */}
      <Button onClick={onClose} className="w-full max-w-sm" size="lg">
        {tSuccess("closeButton")}
      </Button>
    </div>
  );
};

export default SuccessfulOrderMessage;
